'use client';

import { useState } from 'react';
import { TextReveal } from '@/components/ui/text-reveal';
import { Button } from '@/components/ui/button';

import { Badge } from '@/components/ui/badge';
import Earth from '@/components/ui/globe';
import { SparklesCore } from '@/components/ui/sparkles';
import { CircularText } from '@/components/mvpblocks/circular-text';
import { SlidingLogos } from '@/components/ui/sliding-logos';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  ArrowRight,
  Star,
  Sparkles,
  Mail,
  Target,
  Zap,
  Rocket,
  Code,
  Paintbrush,
  ChevronDown,
  Check,

  Phone,
  Palette,
  BrainCircuit,
  LineChart,
  Menu,
  X,
  Globe,
  Users,
  Award
} from 'lucide-react';

// Geometric Hero Component from Variation 2
function ElegantShape({
  className,
  delay = 0,
  width = 400,
  height = 100,
  rotate = 0,
  gradient = 'from-white/[0.08]',
}: {
  className?: string;
  delay?: number;
  width?: number;
  height?: number;
  rotate?: number;
  gradient?: string;
}) {
  return (
    <motion.div
      initial={{
        opacity: 0,
        y: -150,
        rotate: rotate - 15,
      }}
      animate={{
        opacity: 1,
        y: 0,
        rotate: rotate,
      }}
      transition={{
        duration: 2.4,
        delay,
        ease: [0.23, 0.86, 0.39, 0.96],
        opacity: { duration: 1.2 },
      }}
      className={cn('absolute', className)}
    >
      <motion.div
        animate={{
          y: [0, 15, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: 'easeInOut',
        }}
        style={{
          width,
          height,
        }}
        className="relative"
      >
        <div
          className={cn(
            'absolute inset-0 rounded-full',
            'bg-gradient-to-r to-transparent',
            gradient,
            'border-2 border-white/20 backdrop-blur-[2px]',
            'shadow-[0_8px_32px_0_rgba(139,69,19,0.3)]',
            'after:absolute after:inset-0 after:rounded-full',
            'after:bg-[radial-gradient(circle_at_50%_50%,rgba(139,69,19,0.4),transparent_70%)]',
          )}
        />
      </motion.div>
    </motion.div>
  );
}

// FAQ data from variation 1
const faqs = [
  {
    question: 'What makes Celerai Studio unique?',
    answer: 'We combine world-class design with a relentless focus on business growth. Every project is crafted to not just look beautiful, but to drive real business results.',
  },
  {
    question: 'How long does a typical project take?',
    answer: 'Project timelines vary based on scope, but most branding projects take 4-6 weeks, while website development takes 6-10 weeks. We provide detailed timelines during our discovery phase.',
  },
  {
    question: 'Do you work with startups or just established companies?',
    answer: 'We work with passionate founders at all stages - from early-stage startups to established companies looking to rebrand or expand their digital presence.',
  },
  {
    question: 'What\'s included in your branding service?',
    answer: 'Our branding service includes logo design, brand guidelines, color palettes, typography systems, and brand strategy. We create a complete identity system for your business.',
  },
  {
    question: 'Can you help with ongoing marketing after launch?',
    answer: 'Absolutely! We offer ongoing support and can help with digital marketing strategy, content creation, and website maintenance to ensure your continued growth.',
  },
];

// FAQ Item Component
interface FAQItemProps {
  question: string;
  answer: string;
  index: number;
}

function FAQItem({ question, answer, index }: FAQItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.05 }}
      viewport={{ once: true }}
      className="border-border rounded-lg border bg-card/50 backdrop-blur-sm shadow-luxury-sm hover:shadow-luxury-md transition-all duration-300"
    >
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-center justify-between p-6 text-left transition-colors hover:bg-muted/50"
      >
        <span className="text-foreground font-medium">{question}</span>
        <ChevronDown
          className={cn(
            "h-5 w-5 text-muted-foreground transition-transform duration-200",
            isOpen && "rotate-180"
          )}
        />
      </button>

      <div
        className={cn(
          "overflow-hidden transition-all duration-300 ease-out",
          isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
        )}
      >
        <div className="px-6 pb-6">
          <p className="text-muted-foreground leading-relaxed">{answer}</p>
        </div>
      </div>
    </motion.div>
  );
}

export default function FinalLandingPage() {

  return (
    <div className="min-h-screen bg-background">
      {/* PREMIUM DARK HEADER */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-xl border-b border-border shadow-luxury-lg">
        <motion.div
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.8, ease: [0.4, 0, 0.2, 1] }}
          className="max-w-[1400px] mx-auto h-20 px-8"
        >
          <div className="flex items-center justify-between h-full">

            {/* Left side - Logo */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="flex items-center space-x-3"
            >
              <div className="w-10 h-10 bg-primary rounded-xl flex items-center justify-center shadow-luxury-md">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              <div className="text-xl font-bold text-foreground tracking-tight">Celer AI Studio</div>
            </motion.div>

            {/* Center - Navigation */}
            <motion.nav
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.6 }}
              className="hidden md:flex items-center space-x-8"
            >
              <a href="#explorations" className="text-muted-foreground hover:text-primary transition-all duration-300 text-sm font-medium relative group">
                Work
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></div>
              </a>

              <a href="#faq" className="text-muted-foreground hover:text-primary transition-all duration-300 text-sm font-medium relative group">
                FAQ
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></div>
              </a>

              <a href="#contact" className="text-muted-foreground hover:text-primary transition-all duration-300 text-sm font-medium relative group">
                Contact
                <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></div>
              </a>
            </motion.nav>

            {/* Right side - CTA */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7, duration: 0.6 }}
            >
              <Button
                size="sm"
                className="bg-primary hover:bg-primary/90 shadow-luxury-md hover:shadow-luxury-lg transition-all duration-300 px-6"
                asChild
              >
                <a href="mailto:<EMAIL>">
                  <Mail className="h-4 w-4 mr-2" />
                  Contact Us
                </a>
              </Button>
            </motion.div>

            {/* Mobile menu */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.9, duration: 0.6 }}
              className="md:hidden"
            >
              <Button variant="ghost" size="sm" className="p-2">
                <Menu className="h-5 w-5" />
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </header>

      <main className="min-h-screen">
        {/* PROPER GEOMETRIC HERO SECTION FROM VARIATION 2 */}
        <section className="relative flex min-h-screen w-full items-center justify-center overflow-hidden">
          {/* Background gradient - ORIGINAL COLORS */}
          <div className="from-primary/20 absolute inset-0 bg-gradient-to-br via-transparent to-rose-500/20 blur-3xl" />

          {/* Multiple geometric shapes and rectangles */}
          <div className="absolute inset-0 overflow-hidden">
            {/* ORIGINAL HERO COLORS FROM VARIATION 2 */}
            <ElegantShape
              delay={0.3}
              width={600}
              height={140}
              rotate={12}
              gradient="from-indigo-500/70"
              className="top-[15%] left-[-10%] md:top-[20%] md:left-[-5%]"
            />

            <ElegantShape
              delay={0.5}
              width={500}
              height={120}
              rotate={-15}
              gradient="from-rose-400"
              className="top-[70%] right-[-5%] md:top-[75%] md:right-[0%]"
            />

            <ElegantShape
              delay={0.4}
              width={300}
              height={80}
              rotate={-8}
              gradient="from-violet-400"
              className="bottom-[5%] left-[5%] md:bottom-[10%] md:left-[10%]"
            />

            <ElegantShape
              delay={0.6}
              width={200}
              height={60}
              rotate={20}
              gradient="from-amber-500/70"
              className="top-[10%] right-[15%] md:top-[15%] md:right-[20%]"
            />

            <ElegantShape
              delay={0.7}
              width={150}
              height={40}
              rotate={-25}
              gradient="from-cyan-500/70"
              className="top-[5%] left-[20%] md:top-[10%] md:left-[25%]"
            />

            {/* Additional rectangles and geometric shapes */}
            <motion.div
              initial={{ opacity: 0, rotate: 0 }}
              animate={{ opacity: 1, rotate: 45 }}
              transition={{ duration: 2, delay: 0.8 }}
              className="absolute top-[30%] right-[10%] w-24 h-24 bg-primary/30 rounded-lg shadow-luxury-lg"
            />

            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1.5, delay: 1 }}
              className="absolute bottom-[20%] right-[25%] w-16 h-16 bg-accent/40 rounded-full shadow-luxury-md"
            />

            <motion.div
              initial={{ opacity: 0, x: -100 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 2, delay: 0.9 }}
              className="absolute top-[60%] left-[15%] w-32 h-8 bg-primary/25 rounded-full shadow-luxury-sm"
            />

            {/* Background globe */}
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 0.1, scale: 1 }}
              transition={{ duration: 3, delay: 1.2 }}
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 pointer-events-none"
            >
              <Earth
                baseColor={[0.82, 0.41, 0.12]}
                markerColor={[1, 1, 1]}
                glowColor={[0.82, 0.41, 0.12]}
                scale={0.8}
                className="w-full h-full opacity-20"
              />
            </motion.div>


          </div>

          <div className="relative z-10 container mx-auto max-w-6xl px-4 md:px-6">
            <div className="mx-auto max-w-4xl text-center">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="border-primary/30 bg-card/50 mb-6 md:mb-8 inline-flex items-center gap-2 rounded-full border px-3 py-1.5 md:px-4 shadow-luxury-sm backdrop-blur-sm text-sm md:text-base"
              >
                <Star className="h-3 w-3 md:h-4 md:w-4 text-primary mr-1 md:mr-2" />
                <span className="text-foreground font-medium tracking-wide">
                  Intelligent Design & Growth
                </span>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <h1 className="mx-2 md:mx-4 mb-4 md:mb-6 text-3xl sm:text-4xl md:text-6xl lg:text-8xl font-bold tracking-tight leading-tight">
                  <span className="from-foreground to-foreground/80 bg-gradient-to-b bg-clip-text text-transparent block">
                    Branding that
                  </span>
                  <span className="from-primary via-primary/90 bg-gradient-to-r to-rose-500 bg-clip-text text-transparent font-bold block mt-2">
                    Connects. Websites that Convert.
                  </span>
                </h1>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
              >
                <p className="text-muted-foreground mx-auto mb-8 md:mb-10 max-w-2xl px-4 text-sm sm:text-base md:text-lg lg:text-xl leading-relaxed">
                  We are a design and growth studio for passionate founders. We build the strategic brand and digital presence you need to unlock your true business potential.
                </p>
              </motion.div>


            </div>
          </div>

          <div className="from-background to-background/80 pointer-events-none absolute inset-0 bg-gradient-to-t via-transparent" />
        </section>

        {/* SPARKLES LOGO CLOUD - PREMIUM DARK */}
        <section className="section-spacing bg-background relative overflow-hidden">
          <div className="absolute inset-0">
            <SparklesCore
              id="tsparticles-bg"
              background="transparent"
              minSize={0.4}
              maxSize={1.0}
              particleDensity={100}
              className="w-full h-full"
              particleColor="#007CF0"
            />
          </div>

          {/* Simple geometric shapes - dark theme */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              animate={{
                rotate: [0, 360],
                x: [0, 20, 0],
                y: [0, -10, 0]
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
              className="absolute top-20 left-20 w-16 h-16 bg-primary/10 rounded-lg backdrop-blur-sm border border-primary/20"
            />

            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                rotate: [0, -180, -360]
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute top-32 right-32 w-12 h-12 bg-primary/15 rounded-full backdrop-blur-sm"
            />

            <motion.div
              animate={{
                x: [0, -30, 0],
                y: [0, 20, 0],
                rotate: [0, 90, 180]
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                ease: "linear"
              }}
              className="absolute bottom-20 left-1/4 w-8 h-24 bg-primary/8 rounded-full backdrop-blur-sm"
            />

            <motion.div
              animate={{
                rotate: [0, -90, -180],
                scale: [1, 0.8, 1]
              }}
              transition={{
                duration: 18,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute bottom-32 right-20 w-20 h-6 bg-primary/12 rounded-full backdrop-blur-sm"
            />
          </div>

          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: [0.4, 0, 0.2, 1] }}
            viewport={{ once: true }}
            className="relative z-10 mx-auto max-w-2xl text-center"
          >
            <div className="text-center text-3xl mb-14">
              <motion.span
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.8 }}
                viewport={{ once: true }}
                className="text-muted-foreground"
              >
                Trusted by experts.
              </motion.span>

              <br />

              <motion.span
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.8 }}
                viewport={{ once: true }}
                className="text-foreground"
              >
                Used by the leaders.
              </motion.span>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.8 }}
              viewport={{ once: true }}
            >
              <SlidingLogos />
            </motion.div>
          </motion.div>
        </section>

        {/* OUR EXPLORATIONS SECTION FROM VARIATION 2 WITH FLIP CARDS */}
        <section id="explorations" className="section-spacing relative">
          <div className="container mx-auto px-4">
            {/* Circular text in top right */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 0.4, scale: 1 }}
              transition={{ duration: 1.5, delay: 0.5 }}
              viewport={{ once: true }}
              className="absolute top-8 right-8 z-10 hidden lg:block"
            >
              <CircularText
                text="• EXPLORE • DISCOVER • CREATE • "
                spinDuration={25}
                onHover="speedUp"
                size="small"
                className="text-primary opacity-60"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="text-center mb-16"
            >
              <TextReveal
                className="text-section-title text-foreground mb-6"
                split="word"
                from="bottom"
                delay={0.05}
              >
                Our Explorations
              </TextReveal>
              <p className="text-body max-w-2xl mx-auto">
                Discover how we transform ideas into powerful digital experiences through strategic design and innovative solutions.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
              {[
                {
                  title: "Brand Strategy",
                  description: "Strategic positioning that resonates with your audience and drives business growth",
                  icon: <Target className="h-8 w-8 text-primary" />,
                  className: "md:col-span-2",
                  image: "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?w=800&h=400&fit=crop",
                  details: "Complete brand identity systems that establish market presence and drive customer loyalty."
                },
                {
                  title: "Design Systems",
                  description: "Cohesive visual languages that scale with your business",
                  icon: <Palette className="h-8 w-8 text-primary" />,
                  className: "",
                  image: "https://images.unsplash.com/photo-1618761714954-0b8cd0026356?w=400&h=300&fit=crop",
                  details: "Scalable design frameworks that ensure consistency across all touchpoints."
                },
                {
                  title: "Digital Experiences",
                  description: "User-centered designs that convert visitors into customers",
                  icon: <BrainCircuit className="h-8 w-8 text-primary" />,
                  className: "",
                  image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop",
                  details: "Intuitive interfaces that enhance user experience and drive conversions."
                },
                {
                  title: "Growth Strategy",
                  description: "Data-driven approaches to accelerate business growth",
                  icon: <LineChart className="h-8 w-8 text-primary" />,
                  className: "md:col-span-2",
                  image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop",
                  details: "Strategic growth frameworks that turn insights into actionable business results."
                }
              ].map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1, duration: 0.6 }}
                  viewport={{ once: true }}
                  className={cn(
                    "group relative overflow-hidden rounded-2xl bg-card shadow-luxury-md hover:shadow-luxury-xl transition-all duration-700 cursor-pointer min-h-[300px] perspective-1000",
                    item.className
                  )}
                  whileHover={{
                    scale: 1.02,
                    transition: { duration: 0.2 }
                  }}
                >
                  {/* Card container with 3D flip effect */}
                  <div className="relative w-full h-full transform-style-preserve-3d transition-transform duration-700 group-hover:rotate-y-180">

                    {/* Front of card */}
                    <div className="absolute inset-0 p-8 flex flex-col justify-between backface-hidden">
                      <div>
                        <div className="flex items-center gap-3 mb-4">
                          <motion.div
                            whileHover={{ rotate: 360, scale: 1.1 }}
                            transition={{ duration: 0.5 }}
                          >
                            {item.icon}
                          </motion.div>
                          <h3 className="text-card-title text-foreground">{item.title}</h3>
                        </div>
                        <p className="text-muted-foreground mb-6">{item.description}</p>
                      </div>

                    </div>

                    {/* Back of card - flip content */}
                    <div className="absolute inset-0 p-8 flex flex-col justify-center items-center text-center bg-gradient-to-br from-primary/5 to-accent/5 backdrop-blur-sm rotate-y-180 backface-hidden">
                      <motion.div
                        initial={{ scale: 0 }}
                        whileInView={{ scale: 1 }}
                        transition={{ delay: 0.2, duration: 0.5 }}
                        className="mb-6 p-4 bg-primary/10 rounded-full"
                      >
                        {item.icon}
                      </motion.div>
                      <h3 className="text-card-title text-foreground mb-4">{item.title}</h3>
                      <p className="text-muted-foreground text-sm leading-relaxed mb-6">{item.details}</p>
                      <div className="w-12 h-1 bg-gradient-to-r from-primary to-accent rounded-full"></div>
                    </div>
                  </div>

                  {/* Background image with parallax effect */}
                  <div className="absolute inset-0 opacity-5 group-hover:opacity-15 transition-all duration-700 group-hover:scale-110">
                    <img src={item.image} alt={item.title} className="w-full h-full object-cover" />
                  </div>

                  {/* Geometric accent shapes */}
                  <div className="absolute top-4 right-4 w-2 h-2 bg-primary rounded-full opacity-50 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute bottom-4 left-4 w-1 h-8 bg-accent/30 rounded-full group-hover:bg-accent/60 transition-colors duration-500"></div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ SECTION FROM VARIATION 1 */}
        <section id="faq" className="section-spacing bg-muted/20">
          <div className="container mx-auto max-w-6xl px-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
              className="mx-auto mb-12 max-w-2xl text-center"
            >
              <Badge
                variant="outline"
                className="border-primary mb-4 px-3 py-1 text-xs font-medium tracking-wider uppercase text-primary"
              >
                FAQs
              </Badge>

              <TextReveal
                className="text-section-title text-foreground mb-3"
                split="word"
                from="bottom"
                delay={0.05}
              >
                Frequently Asked Questions
              </TextReveal>
              <p className="text-body">
                Everything you need to know about working with Celerai Studio
              </p>
            </motion.div>

            <div className="mx-auto max-w-2xl space-y-2">
              {faqs.map((faq, index) => (
                <FAQItem key={index} {...faq} index={index} />
              ))}
            </div>


          </div>
        </section>

        {/* EXPANDED CONTACT CARD WITH WORKING GLOBE */}
        <section id="contact" className="section-spacing-lg relative overflow-hidden">
          {/* Background geometric shapes for focus */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-0 left-0 w-96 h-96 bg-primary/5 rounded-full blur-3xl" />
            <div className="absolute bottom-0 right-0 w-64 h-64 bg-accent/5 rotate-45 blur-2xl" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-primary/10 rounded-lg rotate-12 blur-xl" />
          </div>

          <div className="relative z-10 container mx-auto max-w-6xl px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              {/* Globe Section - Expanded */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, ease: [0.4, 0, 0.2, 1] }}
                viewport={{ once: true }}
                className="relative order-2 lg:order-1"
              >
                <div className="relative mx-auto max-w-[600px] rounded-2xl p-8 text-center bg-card shadow-luxury-xl">
                  <div className="relative z-10">
                    <TextReveal
                      className="text-section-title text-foreground mb-6"
                      split="word"
                      from="bottom"
                      delay={0.05}
                    >
                      Global Reach, Local Impact
                    </TextReveal>
                    <p className="text-body mb-8">
                      We work with passionate founders worldwide, bringing local expertise to global challenges.
                    </p>

                    {/* Globe with circular text */}
                    <div className="relative mx-auto w-full max-w-[400px] h-[400px] flex items-center justify-center">
                      <Earth
                        baseColor={[0.113, 0.486, 0.941]}
                        markerColor={[1, 1, 1]}
                        glowColor={[0.113, 0.486, 0.941]}
                        scale={1.0}
                        className="w-full h-full"
                      />


                    </div>

                    <div className="mt-8 flex justify-center">
                      <div className="text-center">
                        <div className="text-lg font-medium text-primary mb-2">Global Reach</div>
                        <div className="text-sm text-muted-foreground">Connecting founders worldwide with premium design solutions</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Contact Form Section */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, ease: [0.4, 0, 0.2, 1] }}
                viewport={{ once: true }}
                className="order-1 lg:order-2"
              >
                <div className="relative">
                  <TextReveal
                    className="text-section-title text-foreground mb-6"
                    split="word"
                    from="bottom"
                    delay={0.05}
                  >
                    Ready to Build Your Future?
                  </TextReveal>

                  <p className="text-body mb-8">
                    My name is the founder of Celer AI Studio. We founded this studio to be the partner we wish we had: one that merges world-class design with a relentless focus on business growth. If you're a founder who shares that passion, let's talk.
                  </p>

                  {/* Simple Contact */}
                  <div className="text-center space-y-6">
                    <div className="p-8 rounded-2xl bg-card/30 backdrop-blur-sm shadow-luxury-md">
                      <a
                        href="mailto:<EMAIL>"
                        className="text-2xl font-medium text-foreground hover:text-primary transition-colors duration-300 block"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>
      </main>

      {/* SIMPLE ELEGANT FOOTER */}
      <footer className="border-t border-border/50 bg-background/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">

            {/* Brand */}
            <div className="text-2xl font-bold bg-gradient-to-r from-primary to-rose-500 bg-clip-text text-transparent" style={{ fontFamily: 'Brush Script MT, cursive' }}>
              Celer Studio
            </div>

            {/* Founder */}
            <div className="text-center md:text-right">
              <p className="text-sm text-muted-foreground">
                Founded by <span className="text-foreground font-medium">Sidharth Rajmohan</span>
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                © 2024 Celer Studio. All rights reserved.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
